import { IChatRepository } from '../ChatRepository';
import { Chat } from '@/core/types/chat';
import { chatService } from '@/services/chatService';
import { chatDb } from '../persistence/ChatDatabase';

export class ChatRepositoryImpl implements IChatRepository {
  async findById(chatId: string): Promise<Chat | undefined> {
    try {
      // 首先尝试从 chatDb 获取聊天记录
      const chatRecord = await chatDb.getChat(chatId);
      if (!chatRecord) {
        return undefined;
      }

      // 转换为 Chat 对象
      const chat: Chat = {
        id: chatRecord.id,
        short: chatRecord.short,
        time: chatRecord.time,
        lastMsgPreview: chatRecord.lastMsgPreview,
        messageCount: chatRecord.messageCount,
        msgList: [], // 运行时加载，不存储在这里
      };

      return chat;
    } catch (error) {
      console.error('Error finding chat by id:', error);
      return undefined;
    }
  }

  async create(chat: Partial<Chat>): Promise<Chat> {
    // 使用现有的 chatService 创建聊天
    const newChat = await chatService.createChat(chat.short || '');
    return newChat;
  }

  async update(chatId: string, updates: Partial<Chat>): Promise<void> {
    try {
      // 构建更新参数
      const updateData: any = {};
      
      if (updates.short !== undefined) {
        updateData.short = updates.short;
      }
      
      if (updates.lastMsgPreview !== undefined) {
        updateData.lastMsgPreview = updates.lastMsgPreview;
      }
      
      if (updates.messageCount !== undefined) {
        updateData.messageCount = updates.messageCount;
      }

      // 使用 chatDb 更新聊天
      await chatDb.updateChat(chatId, updateData);
    } catch (error) {
      console.error('Error updating chat:', error);
      throw error;
    }
  }
}
