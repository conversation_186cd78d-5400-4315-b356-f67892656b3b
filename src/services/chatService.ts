import type { Chat } from "@/core/types/chat.ts";
import { chatDb } from "../infrastructure/persistence/ChatDatabase";
import { messageDb } from "../infrastructure/persistence/MessageDatabase";

/**
 * 聊天服务 - 处理会话的创建、更新、删除和加载
 */
export class ChatService {
    /**
     * 加载所有聊天
     */
    async loadAllChats(): Promise<Chat[]> {
        try {
            console.log('[ChatService] 开始从数据库加载聊天列表...');
            const chats = await chatDb.getAllChats();
            console.log('[ChatService] 从数据库获取到', chats.length, '个聊天记录');

            // 转换为Chat对象
            const result = chats.map((chat) => ({
                short: chat.short,
                id: chat.id,
                msgList: [], // 运行时加载，不存储
                time: chat.time,
                lastMsgPreview: chat.lastMsgPreview,
                messageCount: chat.messageCount,
            }));

            console.log('[ChatService] 转换后的聊天列表:', result);
            return result;
        } catch (error) {
            console.error("[ChatService] 加载聊天列表失败:", error);
            return [];
        }
    }

    /**
     * 创建新聊天
     */
    async createChat(short: string = ""): Promise<Chat> {
        // 使用chatDb创建新聊天
        const chatRecord = await chatDb.createNewChat(short);

        // 转换为Chat对象
        return {
            short: chatRecord.short,
            id: chatRecord.id,
            msgList: [],
            time: chatRecord.time,
            messageCount: chatRecord.messageCount,
            lastMsgPreview: chatRecord.lastMsgPreview,
        };
    }

    /**
     * 删除聊天
     */
    async removeChat(id: string): Promise<boolean> {
        try {
            // 从chatDb中删除
            await chatDb.deleteChat(id);

            // 从messageDb中删除消息
            await messageDb.deleteChatMessages(id);

            console.log("删除会话成功");
            return true;
        } catch (error) {
            console.error("删除会话失败", error);
            return false;
        }
    }

    /**
     * 更新聊天
     */
    async updateChat(id: string, chat: Partial<Chat>): Promise<boolean> {
        try {
            // 更新chatDb
            const updateData: Partial<{
                short: string;
                lastMsgPreview: string;
                messageCount: number;
            }> = {};

            // 只有在提供了字段时才更新
            if (chat.short !== undefined) updateData.short = chat.short;
            if (chat.lastMsgPreview !== undefined)
                updateData.lastMsgPreview = chat.lastMsgPreview;
            if (chat.messageCount !== undefined)
                updateData.messageCount = chat.messageCount;

            await chatDb.updateChat(id, updateData);

            console.info("更新会话成功");
            return true;
        } catch (error) {
            console.error("更新会话失败", id, error);
            return false;
        }
    }
}

// 导出单例实例
export const chatService = new ChatService();
