<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from "vue";
import { imageDb } from "@/infrastructure/persistence/ImageDatabase";
import type { ImageData } from "@/core/types/chat.ts";
import { NSpin } from "naive-ui"; // For loading indicator

const props = defineProps<{ imageData: ImageData }>();
const imageUrl = ref<string | null>(null);
const isLoading = ref(true);
const hasError = ref(false);

async function loadImage() {
    if (!props.imageData || typeof props.imageData.imageId !== "number") {
        console.error(
            "ImageDisplayComponent: Invalid imageData or imageId",
            props.imageData
        );
        isLoading.value = false;
        hasError.value = true;
        return;
    }
    isLoading.value = true;
    hasError.value = false;
    try {
        const blobUrl = await imageDb.getBlobUrl(props.imageData.imageId);
        if (blobUrl) {
            imageUrl.value = blobUrl;
        } else {
            console.error(
                `ImageDisplayComponent: Failed to get blob URL for imageId ${props.imageData.imageId}`
            );
            hasError.value = true;
        }
    } catch (error) {
        console.error(
            `ImageDisplayComponent: Error loading image ${props.imageData.imageId}`,
            error
        );
        hasError.value = true;
    } finally {
        isLoading.value = false;
    }
}

onMounted(() => {
    loadImage();
});

watch(
    () => props.imageData.imageId,
    (newId, oldId) => {
        if (newId !== oldId) {
            if (imageUrl.value) {
                imageDb.releaseBlobUrl(imageUrl.value); // Release old URL if it exists
                imageUrl.value = null; // Reset before loading new one
            }
            loadImage();
        }
    }
);

onUnmounted(() => {
    if (imageUrl.value) {
        imageDb.releaseBlobUrl(imageUrl.value);
    }
});
</script>

<template>
    <div class="image-display-container my-2">
        <div v-if="isLoading" class="flex items-center justify-center p-4">
            <n-spin size="small" />
            <span class="ml-2 text-sm text-gray-500">图片加载中...</span>
        </div>
        <img
            v-else-if="imageUrl && !hasError"
            :src="imageUrl"
            :alt="imageData.alt || '用户图片'"
            class="max-w-full h-auto rounded-md shadow-md"
            @error="hasError = true"
        />
        <div
            v-else-if="hasError"
            class="text-red-500 text-sm p-2 bg-red-100 border border-red-300 rounded"
        >
            图片加载失败 (ID: {{ imageData.imageId }})
        </div>
    </div>
</template>

<style scoped>
.image-display-container img {
    display: block; /* Prevents bottom space under image */
    max-height: 400px; /* Limit max height for very tall images */
}
</style>
