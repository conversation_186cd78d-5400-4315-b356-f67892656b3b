<script setup>
import MsgList from "../../components/MsgList/MsgList.vue";
import EditArea from "@/components/EditArea/EditArea.vue";
import Home from "../../components/Home/Home.vue";
import ChatList from "../../components/ChatList/ChatList.vue";
import AppHeader from "../../components/AppHeader/AppHeader.vue";
import { useEnvStore } from "../../store/useEnvStore";
import { useChatStore } from "../../store/useChatStore.ts";
import { useModelStore } from "@/store/useModelStore.ts";
import { computed, watch, ref } from "vue";
import { useNotification, useMessage, NAlert, NSpin } from "naive-ui";
import { useRouter } from "vue-router";
import { WarningOutlined } from "@vicons/antd";

const envStore = useEnvStore();
const store = useChatStore();
const modelStore = useModelStore();
const isMsgListEmpty = computed(() => store.activeChat.msgList.length === 0);
const hasActiveChat = computed(() => store.activeChatId !== "0");
const notification = useNotification();
const message = useMessage();
const router = useRouter();
</script>

<template>
    <ChatList />
    <!-- 右侧布局 min-width:0 重要属性 允许内容收窄 -->
    <div class="flex-grow max-w-[100dvw] h-[100dvh] flex flex-col min-w-0">
        <app-header class="shrink-0" />
        <n-flex vertical class="app flex-grow" :class="{ win: envStore.isWin }">
            <div :wrap="false" class="main-content min-h-0 flex justify-center">
                <n-flex
                    :wrap="false"
                    class="right-layout md:max-w-[48rem]"
                    vertical
                >
                    <!-- 添加蒙层 -->
                    <div
                        v-if="!store.isSidebarCollapsed"
                        class="mask md:hidden"
                        @click="store.isSidebarCollapsed = true"
                    ></div>
                    <!-- 显示API配置错误提示 -->
                    <n-alert
                        v-if="!modelStore.hasValidProvider"
                        title="未配置有效API"
                        type="warning"
                        class="mx-4 mt-4"
                    >
                        <template #icon>
                            <n-icon>
                                <warning-outlined />
                            </n-icon>
                        </template>
                        您尚未配置有效的API，或没有开启任何API供应商，请前往设置页面进行配置。

                        <n-button @click="router.push({ name: 'Setting' })">
                            前往设置
                        </n-button>
                    </n-alert>

                    <div
                        class="chat-layout flex flex-col max-w-[max(48rem,50%)]"
                        :class="{
                            'justify-center':
                                (!hasActiveChat || isMsgListEmpty) && !store.isLoadingMessages,
                        }"
                    >
                        <template v-if="store.isLoadingMessages">
                            <div
                                class="loading-indicator flex justify-center items-center h-full"
                            ></div>
                        </template>
                        <template v-else>
                            <!-- 有活动会话且消息列表不为空时显示聊天界面 -->
                            <template v-if="hasActiveChat && !isMsgListEmpty">
                                <msg-list />
                                <edit-area />
                            </template>
                            <!-- 没有活动会话或消息列表为空时显示Home页面 -->
                            <template v-if="!hasActiveChat || isMsgListEmpty">
                                <Home />
                            </template>
                        </template>
                    </div>
                </n-flex>
            </div>
        </n-flex>
    </div>
</template>

<style scoped lang="less">
.app {
    height: 100%;
    width: 100%;
    overflow: hidden;
    gap: 0 !important;
    background-color: var(--color-background);

    .main-content {
        flex-grow: 1;
        gap: 0 !important;
        height: auto;

        .right-layout {
            position: relative; // 添加相对定位
            flex-grow: 1;
            height: 100%;
            overflow-x: hidden;

            .chat-layout {
                margin: 0 auto;
                width: 100%;
                flex-basis: auto;
                flex-grow: 1;
                min-height: 0;
                display: flex;
                flex-direction: column;
                padding: 0 20px;
            }

            .mask {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.4);
                z-index: 99;
            }
        }
    }
}
</style>
